# تعليمات التثبيت - موديول إجبار تغيير كلمة المرور

## متطلبات النظام

- Odoo 15.0 Community Edition
- Python 3.7+
- صلاحيات إدارية في Odoo

## خطوات التثبيت

### 1. نسخ الملفات

```bash
# انسخ مجلد force_password_change إلى مجلد addons في Odoo
cp -r force_password_change /path/to/odoo/addons/
```

### 2. إعادة تشغيل خادم Odoo

```bash
# أعد تشغيل خادم Odoo
sudo systemctl restart odoo
# أو
python3 odoo-bin -c /path/to/odoo.conf
```

### 3. تحديث قائمة الموديولات

1. سجل دخول إلى Odoo كمدير
2. اذهب إلى `الإعدادات > التطبيقات`
3. انقر على `تحديث قائمة التطبيقات`

### 4. تثبيت الموديول

1. في صفحة التطبيقات، ابحث عن "Force Password Change"
2. انقر على `تثبيت`

## التحقق من التثبيت

### 1. فحص المستخدمين

1. اذهب إلى `الإعدادات > المستخدمون والشركات > المستخدمون`
2. تأكد من وجود حقل "يجب تغيير كلمة المرور" في نموذج المستخدم

### 2. إنشاء مستخدم تجريبي

1. أنشئ مستخدم جديد
2. تأكد من أن حقل "يجب تغيير كلمة المرور" محدد تلقائياً
3. احفظ المستخدم

### 3. اختبار تسجيل الدخول

1. سجل خروج من حساب المدير
2. سجل دخول بالمستخدم الجديد
3. يجب أن يتم توجيهك إلى صفحة تغيير كلمة المرور

## استكشاف الأخطاء

### خطأ في التثبيت

```bash
# تحقق من سجلات Odoo
tail -f /var/log/odoo/odoo.log
```

### الموديول غير ظاهر

1. تأكد من أن المجلد في المسار الصحيح
2. تأكد من صلاحيات الملفات
3. أعد تشغيل الخادم

### خطأ في قاعدة البيانات

```sql
-- تحقق من وجود الحقل في قاعدة البيانات
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'res_users' AND column_name = 'must_change_password';
```

## إلغاء التثبيت

1. اذهب إلى `الإعدادات > التطبيقات`
2. ابحث عن "Force Password Change"
3. انقر على `إلغاء التثبيت`

## الدعم الفني

في حالة مواجهة مشاكل:

1. تحقق من سجلات النظام
2. تأكد من توافق الإصدارات
3. راجع ملف README.md للمزيد من التفاصيل
