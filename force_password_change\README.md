# Force Password Change Module

## Overview

This module forces new users to change their password when they log in for the first time in Odoo 15 Community Edition.

## Features

- **Automatic Flag Setting**: When a new user is created, the `must_change_password` flag is automatically set to `True`
- **Login Interception**: The module intercepts the login process and redirects users who need to change their password
- **User-Friendly Interface**: Provides a clean, responsive password change form
- **Security Validation**: Includes password strength validation and confirmation matching
- **Clear Messaging**: Shows clear warning messages to users about the requirement
- **Seamless Integration**: Works seamlessly with Odoo's existing authentication system

## Installation

1. Copy the `force_password_change` folder to your Odoo addons directory
2. Update the addons list: `Settings > Apps > Update Apps List`
3. Search for "Force Password Change" and install the module

## Usage

### For Administrators

1. Go to `Settings > Users & Companies > Users`
2. Create a new user or edit an existing user
3. The `Must Change Password` field will be visible in the user form
4. For new users, this field is automatically set to `True`
5. You can manually set this field to `True` for existing users if needed

### For Users

1. When a user with `must_change_password = True` logs in, they will be redirected to a password change form
2. The user must enter:
   - Current password
   - New password (minimum 6 characters)
   - Confirmation of new password
3. After successful password change, the user will be redirected to their intended destination
4. The `must_change_password` flag will be automatically set to `False`

## Technical Details

### Models

- **res.users**: Extended with `must_change_password` boolean field
- **password.change.wizard**: Transient model for password change wizard (optional)

### Controllers

- **ForcePasswordChangeController**: Extends AuthSignupHome to intercept login and handle password change

### Views

- User form view with `must_change_password` field
- User tree view showing password change status
- Password change form template
- Search filters for users who must change password

### Security

- Proper access rights for all models
- Password validation (minimum length, confirmation matching)
- CSRF protection on forms
- Secure password handling

## Configuration

No additional configuration is required. The module works out of the box.

## Compatibility

- Odoo 15.0 Community Edition
- Compatible with existing authentication modules
- Works with multi-company setups

## Troubleshooting

### User Not Redirected to Password Change

1. Check if the user has `must_change_password = True`
2. Verify the module is properly installed and activated
3. Check server logs for any errors

### Password Change Fails

1. Ensure the current password is correct
2. Check password strength requirements (minimum 6 characters)
3. Verify new password and confirmation match
4. Check server logs for detailed error messages

### Module Installation Issues

1. Ensure all dependencies are installed (`base`, `web`, `auth_signup`)
2. Check file permissions in the addons directory
3. Restart the Odoo server after installation

## Support

For support and bug reports, please contact your system administrator or the module developer.

## License

This module is licensed under LGPL-3.
