# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError, ValidationError


class TestForcePasswordChange(TransactionCase):

    def setUp(self):
        super(TestForcePasswordChange, self).setUp()
        self.Users = self.env['res.users']
        self.PasswordWizard = self.env['password.change.wizard']

    def test_new_user_must_change_password(self):
        """Test that new users have must_change_password set to True"""
        user = self.Users.create({
            'name': 'Test User',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
            'password': 'testpassword123',
        })
        
        self.assertTrue(user.must_change_password, 
                       "New user should have must_change_password set to True")

    def test_password_change_resets_flag(self):
        """Test that changing password sets must_change_password to False"""
        user = self.Users.create({
            'name': 'Test User 2',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
            'password': 'testpassword123',
        })
        
        # Verify flag is initially True
        self.assertTrue(user.must_change_password)
        
        # Change password
        user.with_context(user_changing_own_password=True).change_password(
            'testpassword123', 'newpassword123'
        )
        
        # Verify flag is now False
        self.assertFalse(user.must_change_password, 
                        "must_change_password should be False after password change")

    def test_password_wizard_validation(self):
        """Test password wizard validation"""
        wizard = self.PasswordWizard.create({
            'current_password': 'oldpass',
            'new_password': 'newpass123',
            'confirm_password': 'differentpass',
        })
        
        # Test password mismatch validation
        with self.assertRaises(ValidationError):
            wizard._check_password_match()

    def test_password_strength_validation(self):
        """Test password strength validation"""
        wizard = self.PasswordWizard.create({
            'current_password': 'oldpass',
            'new_password': 'short',
            'confirm_password': 'short',
        })
        
        # Test minimum length validation
        with self.assertRaises(ValidationError):
            wizard._check_password_strength()

    def test_check_password_change_required(self):
        """Test the check_password_change_required method"""
        user = self.Users.create({
            'name': 'Test User 3',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
            'password': 'testpassword123',
        })
        
        # Should return True for new user
        self.assertTrue(self.Users.check_password_change_required(user.id))
        
        # Change the flag
        user.must_change_password = False
        
        # Should return False now
        self.assertFalse(self.Users.check_password_change_required(user.id))

    def test_explicit_must_change_password_false(self):
        """Test creating user with must_change_password explicitly set to False"""
        user = self.Users.create({
            'name': 'Test User 4',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'must_change_password': False,
        })
        
        self.assertFalse(user.must_change_password, 
                        "User should respect explicitly set must_change_password value")
