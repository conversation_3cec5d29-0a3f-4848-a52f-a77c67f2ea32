/* Force Password Change Styles */

.password-change-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.password-change-card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    border: none;
    max-width: 400px;
    width: 100%;
}

.password-change-card .card-header {
    border-radius: 10px 10px 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.password-change-card .card-body {
    padding: 2rem;
}

.password-change-card .form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.password-change-card .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.password-change-card .btn {
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.password-change-card .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.password-change-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.password-change-card .btn-secondary {
    background-color: #6c757d;
    border: none;
}

.password-change-card .btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.alert {
    border-radius: 8px;
    border: none;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.card-footer {
    border-radius: 0 0 10px 10px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .password-change-card {
        margin: 1rem;
    }
    
    .password-change-card .card-body {
        padding: 1.5rem;
    }
}
