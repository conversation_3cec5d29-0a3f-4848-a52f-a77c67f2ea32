# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError


class ResUsers(models.Model):
    _inherit = 'res.users'

    must_change_password = fields.Boolean(
        string='Must Change Password',
        default=True,
        help='If checked, the user will be forced to change their password on next login'
    )

    @api.model
    def create(self, vals):
        """Override create to set must_change_password to True for new users"""
        # Set must_change_password to True by default for new users
        if 'must_change_password' not in vals:
            vals['must_change_password'] = True
        
        user = super(ResUsers, self).create(vals)
        return user

    def write(self, vals):
        """Override write to handle password changes"""
        # If password is being changed, set must_change_password to False
        if 'password' in vals and vals.get('password'):
            # Only set to False if the user is changing their own password
            # and they currently have must_change_password = True
            if self.env.context.get('user_changing_own_password'):
                vals['must_change_password'] = False
        
        return super(<PERSON>s<PERSON><PERSON><PERSON>, self).write(vals)

    def change_password(self, old_passwd, new_passwd):
        """Override change_password to mark password as changed"""
        # Set context to indicate user is changing their own password
        self = self.with_context(user_changing_own_password=True)
        result = super(ResUsers, self).change_password(old_passwd, new_passwd)

        # Ensure must_change_password is set to False after successful password change
        if self.must_change_password:
            self.sudo().write({'must_change_password': False})

        return result

    def _set_password(self):
        """Override _set_password to handle password changes"""
        # If password is being changed and user is changing their own password
        if self.env.context.get('user_changing_own_password'):
            # Call parent method first
            super(ResUsers, self)._set_password()
            # Then update must_change_password flag
            for user in self:
                if user.must_change_password:
                    user.sudo().write({'must_change_password': False})
        else:
            super(ResUsers, self)._set_password()

    @api.model
    def check_password_change_required(self, user_id):
        """Check if user needs to change password"""
        user = self.browse(user_id)
        return user.must_change_password if user.exists() else False
