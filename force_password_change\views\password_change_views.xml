<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Password Change Wizard Form -->
        <record id="view_password_change_wizard_form" model="ir.ui.view">
            <field name="name">password.change.wizard.form</field>
            <field name="model">password.change.wizard</field>
            <field name="arch" type="xml">
                <form string="Change Password">
                    <div class="alert alert-warning" role="alert">
                        <strong>Password Change Required!</strong><br/>
                        You must change your password before continuing to use the system.
                    </div>
                    <group>
                        <group>
                            <field name="current_password" password="True" placeholder="Enter current password"/>
                            <field name="new_password" password="True" placeholder="Enter new password"/>
                            <field name="confirm_password" password="True" placeholder="Confirm new password"/>
                        </group>
                    </group>
                    <footer>
                        <button name="action_change_password" string="Change Password" type="object" class="btn-primary"/>
                        <button name="action_cancel" string="Cancel &amp; Logout" type="object" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Password Change Wizard Action -->
        <record id="action_password_change_wizard" model="ir.actions.act_window">
            <field name="name">Change Password</field>
            <field name="res_model">password.change.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="view_password_change_wizard_form"/>
        </record>
    </data>
</odoo>
