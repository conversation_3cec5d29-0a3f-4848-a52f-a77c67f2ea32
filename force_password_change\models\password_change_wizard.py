# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class PasswordChangeWizard(models.TransientModel):
    _name = 'password.change.wizard'
    _description = 'Password Change Wizard'

    current_password = fields.Char(
        string='Current Password',
        required=True,
        help='Enter your current password'
    )
    new_password = fields.Char(
        string='New Password',
        required=True,
        help='Enter your new password'
    )
    confirm_password = fields.Char(
        string='Confirm New Password',
        required=True,
        help='Confirm your new password'
    )

    @api.constrains('new_password', 'confirm_password')
    def _check_password_match(self):
        """Validate that new password and confirmation match"""
        for record in self:
            if record.new_password != record.confirm_password:
                raise ValidationError(_('New password and confirmation do not match.'))

    @api.constrains('new_password')
    def _check_password_strength(self):
        """Basic password strength validation"""
        for record in self:
            if len(record.new_password) < 6:
                raise ValidationError(_('Password must be at least 6 characters long.'))

    def action_change_password(self):
        """Change the user's password"""
        self.ensure_one()
        
        # Validate passwords match
        if self.new_password != self.confirm_password:
            raise UserError(_('New password and confirmation do not match.'))
        
        # Get current user
        current_user = self.env.user
        
        try:
            # Change password using Odoo's built-in method
            current_user.change_password(self.current_password, self.new_password)
            
            # Return action to redirect to main page
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
            
        except Exception as e:
            raise UserError(_('Failed to change password: %s') % str(e))

    def action_cancel(self):
        """Cancel password change and logout"""
        # Logout the user
        return {
            'type': 'ir.actions.act_url',
            'url': '/web/session/logout',
            'target': 'self',
        }
