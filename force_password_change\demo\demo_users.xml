<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo user that must change password -->
        <record id="demo_user_must_change" model="res.users">
            <field name="name">Demo User (Must Change Password)</field>
            <field name="login"><EMAIL></field>
            <field name="email"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="must_change_password" eval="True"/>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_user')])]"/>
        </record>
        
        <!-- Demo user that doesn't need to change password -->
        <record id="demo_user_no_change" model="res.users">
            <field name="name">Demo User (No Change Required)</field>
            <field name="login"><EMAIL></field>
            <field name="email"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="must_change_password" eval="False"/>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_user')])]"/>
        </record>
    </data>
</odoo>
