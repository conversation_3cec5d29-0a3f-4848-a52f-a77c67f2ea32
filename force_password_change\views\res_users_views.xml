<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Add must_change_password field to user form -->
        <record id="view_users_form_inherit" model="ir.ui.view">
            <field name="name">res.users.form.inherit.force.password.change</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='active']" position="after">
                    <field name="must_change_password" 
                           attrs="{'invisible': [('id', '=', False)]}"
                           help="If checked, the user will be forced to change their password on next login"/>
                </xpath>
            </field>
        </record>

        <!-- Add must_change_password field to user tree view -->
        <record id="view_users_tree_inherit" model="ir.ui.view">
            <field name="name">res.users.tree.inherit.force.password.change</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='login_date']" position="after">
                    <field name="must_change_password" string="Must Change Password"/>
                </xpath>
            </field>
        </record>

        <!-- Add must_change_password field to user search view -->
        <record id="view_users_search_inherit" model="ir.ui.view">
            <field name="name">res.users.search.inherit.force.password.change</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='inactive']" position="after">
                    <filter name="must_change_password" 
                            string="Must Change Password" 
                            domain="[('must_change_password', '=', True)]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
