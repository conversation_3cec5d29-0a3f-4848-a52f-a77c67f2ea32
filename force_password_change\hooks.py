# -*- coding: utf-8 -*-

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def post_init_hook(cr, registry):
    """Post-installation hook to set up initial data"""
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # Log installation
    _logger.info("Force Password Change module installed successfully")
    
    # Optionally set must_change_password for existing users without passwords
    users_without_password = env['res.users'].search([
        ('password', '=', False),
        ('active', '=', True),
        ('id', '!=', SUPERUSER_ID)
    ])
    
    if users_without_password:
        users_without_password.write({'must_change_password': True})
        _logger.info(f"Set must_change_password for {len(users_without_password)} users without passwords")


def uninstall_hook(cr, registry):
    """Pre-uninstallation hook to clean up data"""
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # Reset must_change_password for all users
    users = env['res.users'].search([('must_change_password', '=', True)])
    if users:
        users.write({'must_change_password': False})
        _logger.info(f"Reset must_change_password for {len(users)} users")
    
    _logger.info("Force Password Change module uninstalled successfully")
