# -*- coding: utf-8 -*-

import werkzeug
from odoo import http, _
from odoo.http import request
from odoo.addons.web.controllers.main import Home
from odoo.addons.auth_signup.controllers.main import AuthSignupHome


class ForcePasswordChangeController(AuthSignupHome):

    @http.route()
    def web_login(self, redirect=None, **kw):
        """Override web_login to check if password change is required"""
        response = super(ForcePasswordChangeController, self).web_login(redirect=redirect, **kw)

        # Check if login was successful and user needs to change password
        if request.httprequest.method == 'POST' and request.params.get('login_success'):
            if request.session.uid:
                user = request.env['res.users'].sudo().browse(request.session.uid)
                if user.exists() and user.must_change_password:
                    # Store the original redirect URL in session
                    if redirect:
                        request.session['force_password_change_redirect'] = redirect
                    # Redirect to password change page
                    return request.redirect('/force_password_change/change_password')

        return response

    @http.route('/force_password_change/change_password', type='http', auth='user', website=True)
    def change_password_form(self, **kw):
        """Display password change form"""
        user = request.env.user
        
        # Check if user really needs to change password
        if not user.must_change_password:
            return request.redirect('/web')
        
        values = {
            'user': user,
            'error': kw.get('error'),
            'message': kw.get('message'),
        }
        
        return request.render('force_password_change.change_password_form', values)

    @http.route('/force_password_change/submit_password', type='http', auth='user', methods=['POST'], website=True, csrf=False)
    def submit_password_change(self, **post):
        """Process password change form submission"""
        user = request.env.user

        # Check if user really needs to change password
        if not user.must_change_password:
            # Get original redirect URL from session
            redirect_url = request.session.pop('force_password_change_redirect', '/web')
            return request.redirect(redirect_url)

        current_password = post.get('current_password', '')
        new_password = post.get('new_password', '')
        confirm_password = post.get('confirm_password', '')

        # Validate input
        if not current_password or not new_password or not confirm_password:
            return request.redirect('/force_password_change/change_password?error=' +
                                  werkzeug.urls.url_quote(_('All fields are required.')))

        if new_password != confirm_password:
            return request.redirect('/force_password_change/change_password?error=' +
                                  werkzeug.urls.url_quote(_('New password and confirmation do not match.')))

        if len(new_password) < 6:
            return request.redirect('/force_password_change/change_password?error=' +
                                  werkzeug.urls.url_quote(_('Password must be at least 6 characters long.')))

        # Check if new password is different from current password
        if current_password == new_password:
            return request.redirect('/force_password_change/change_password?error=' +
                                  werkzeug.urls.url_quote(_('New password must be different from current password.')))

        try:
            # Change password
            user.change_password(current_password, new_password)

            # Get original redirect URL from session
            redirect_url = request.session.pop('force_password_change_redirect', '/web')

            # Redirect to original page or main page with success message
            return request.redirect(redirect_url + ('&' if '?' in redirect_url else '?') +
                                  'message=' + werkzeug.urls.url_quote(_('Password changed successfully!')))

        except Exception as e:
            error_msg = _('Failed to change password: %s') % str(e)
            return request.redirect('/force_password_change/change_password?error=' +
                                  werkzeug.urls.url_quote(error_msg))

    @http.route('/force_password_change/logout', type='http', auth='user', website=True)
    def force_logout(self, **kw):
        """Logout user if they cancel password change"""
        request.session.logout(keep_db=True)
        return request.redirect('/web/login')
